<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd"
    logicalFilePath="2024/general-domain">

    <changeSet id="DEV-2709-st-referralaspects-bldg" author="neale" context="1.1-base-data">
        <insert tableName="servicetypes_taskdefinitions">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="40"/>
            <column name="version" valueNumeric="0"/>
            <column name="taskDefinitionId" valueNumeric="18"/>  <!-- referralView -->
            <column name="servicetypeId" valueNumeric="-100"/>
        </insert>
        <insert tableName="servicetypes_taskdefinitions">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="50"/>
            <column name="version" valueNumeric="0"/>
            <column name="taskDefinitionId" valueNumeric="50"/>  <!-- close (e.g. sold or end of lease) -->
            <column name="servicetypeId" valueNumeric="-100"/>
        </insert>
        <insert tableName="servicetypes_taskdefinitions">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="99"/>
            <column name="version" valueNumeric="0"/>
            <column name="taskDefinitionId" valueNumeric="11"/>  <!-- endFlow -->
            <column name="servicetypeId" valueNumeric="-100"/>
        </insert>
    </changeSet>

    <changeSet id="20240601-1-add-charge-category-to-fixed-container" author="copilot">
        <comment>Add chargeCategoryId column to bldg_fixed table to support service charge categories</comment>
        <addColumn tableName="bldg_fixed">
            <column name="chargeCategoryId" type="int">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addForeignKeyConstraint
            baseTableName="bldg_fixed"
            baseColumnNames="chargeCategoryId"
            constraintName="fk_bldg_fixed_charge_category"
            referencedTableName="cfg_list_definitions"
            referencedColumnNames="id"
            />
    </changeSet>

    <!-- remove the chargeCategoryId, we need to match multiple charge categories to a building -->
    <changeSet id="20240601-1-remove-charge-category-to-fixed-container" author="copilot">
        <comment>Remove chargeCategoryId column from bldg_fixed table</comment>
        <dropColumn tableName="bldg_fixed" columnName="chargeCategoryId"/>
    </changeSet>



</databaseChangeLog>
