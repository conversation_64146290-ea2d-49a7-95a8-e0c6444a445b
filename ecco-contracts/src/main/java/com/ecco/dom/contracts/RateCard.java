package com.ecco.dom.contracts;

import com.ecco.infrastructure.entity.AbstractIntKeyedEntity;
import lombok.*;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalTime;
import java.util.List;
import java.util.Set;


/**
 * A RateCard is a grouping of charges that can be associated with a contract. The exact charges to use are determined
 * from calculations below.
 * NB RateCards came from the padding out of financial matrix thoughts on ECCO-831, and 567037976ae47c75eb4a54d6e2d3f780116b2017.
 *
 * RateCard(s) cover the costs of items on an independent period of time (eg amendments to cost) unlike a ServiceAgreement
 * which covers a period of time of a contract, and DemandSchedule's cover the visits within that.
 * Its possible that a RateCard becomes just a dummy record that exactly matches the period of a contract - but its safe to leave in for now.
 *
 * RateCard(s) hold only a date. There was an idea of it holding the categoryType (the AppointmentSchedule.AppointmentType or ResourceSchedule.ResourceType)
 * but its difficult to determine if the 'appointmentType' really is the differentiator on a RateCard when there could be
 * other metrics, such as staffBand that determine which RateCard to use. Specifying thins in a RateCard simply forces a
 * hierarchy that may not be wanted. To keep things flexible, we just hold everything on the RateCardEntry(s) as flattened data.
 *
 * For example, the below examples show that RateCardEntry charges relate to a number of factors:
 *  - appointment type (eg one-to-one / wake-up) or resource type (eg bed resource)
 *  - date/time - bank hol/sunday
 *  - staffBand - skilled worker (could be determined/assumed from contract and appointment type??)
 *  - outcome - attended / cancelled etc
 *
 * Examples of RateCard and RateCardEntry(s) are:
 *      RateCard - <name>,   RateCardEntry - <appointmentType>
 *                                         - <outcome>
 *                                         - <temporalUnitCost><temporalUnit> / fixedCost
 *                                         - <payElement> - on-call/visit/night:
 *                                         - <mileageClaim>
 *      RateCard - standard, RateCardEntry - attended 50hr [would be the defaultEntry]
 *      RateCard - standard, RateCardEntry - late cancellation 30hr
 *      RateCard - standard, RateCardEntry - no show 50hr
 *      RateCard - standard, RateCardEntry - cancelled 0hr
 *      RateCard - standard, RateCardEntry - custom defined [chargeType]
 *      RateCard - standard, RateCardEntry - non sunday/bank hol - SBH 20hr [payElement]
 *      RateCard - standard, RateCardEntry - SBH 24/hr
 *      RateCard - standard, RateCardEntry - Waking Nights<appointmentType> - 35/nt
 *      RateCard - bespoke,  RateCardEntry - framework (ServiceAgreement.ContractType), user-selectable
 *      RateCard - bespoke,  RateCardEntry - individual, user-selectable
 *
 *  The calculation would be:
 *  - from the evidence (if available), get the
 *      - actual date&time
 *      - author
 *  - from the calendar event, get the
 *      - expected date&time
 *      - outcome (if available) NB we are best using our events table, not evidence (and not standard calendar systems which aren't good for past info)
 *      - rateCardName and rateCard from the events demandSchedule - see SREventDecorator (or on events / eventsrecurring) and DEV-14 (as currently saved 'null')
 *  - then find the rate card... which might NOT be data-linked...
 *  -   lookup the rate card - if there is a RateCard use that (its fixed), otherwise lookup the right dated RateCard from the available list in its ServiceAgreement
 *      NB looking up the right rate card implies no overlapping RateCards for a ServiceAgreement (error when associating with ServiceAgreement)
 *  -   lookup the rate entry according to the info available: appointment type (one-to-one) / date&time / staffBand / outcome
 *      - this may need to form some priority search order, eg appointment type, then outcome, then staffBand (could leave date&time as part of outcome)
 *
 * The calculation could/should be done at several times:
 *      - when creating schedules/ad-hoc
 *      - rota planning
 *      - evidencing or verifying/invoicing
 *      - upon request, for when a new RateCard is made and want to update charges in-flight,
 *      A warning should be given if the calculation fails to find a RateCardEntry due to non-matching data.
 *      NB A warning should be provided on creating schedules/ad-hoc about going over a time? limit (from ServiceAgreement's or a running total from billing and in-flight?) ECCO-1462
 *
 * The calculation overlaps with the verification / billing / invoicing:
 *          see draft invoicing notes - https://eccosolutions.atlassian.net/browse/DEV-1
 *              remaining work there is: work with invoiceId can't be changed (cmd handler?) / update net/rax / remove invoice line / finalise invoice / xml format
 *          rounding 5 mins (calculation to use whole temporalUnit's in RateCardEntry)
 *          charging 2x30mins rather than 1x60mins (would be separate RateCardEntry's, and show choices allowed)
 *          can include meal charges/rent charges/service charges (eg respite)
 *          can exclude charges via just be crediting/adjusting the invoice lines not deleting (eg extra care includes first few visits)
 */
@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "fin_ratecards")
public class RateCard extends AbstractIntKeyedEntity {

    private static final long serialVersionUID = 1L;

    public enum PartsOfWeek { WEEKDAY, WEEKEND, WEEKDAY_WEEKEND, BANKHOLIDAY };

    /**
     * The name of the rate card which should be unique within the set of cards for the contract.
     * eg 'standard provision'
     */
    String name;

    /**
     * Applicable range for the RateCard. This could differ to the ServiceAgreement allowing charges to change, but it could
     * just a dummy record that exactly matches the period of a contract - but its safe to leave in for now.
     */
    @Column(name="startInstant", nullable = false)
    Instant startInstant;
    @Column(name="endInstant")
    Instant endInstant;

    /**
     * Level at which a warning should be provided.
     * This is assumed to be based on duration, without any other factor (eg categoryType)
     * otherwise we get into complex scenarios for which we could just specify another RateCard.
     * Or this should be moved to the contract.
     */
    Integer advisoryTotalDuration;

    /**
     * Level at which a warning should be provided.
     * This is assumed to be based on charge.
     * This is perhaps less expected/used than duration.
     * Or this should be moved to the contract.
     */
    BigDecimal advisoryTotalCharge;

    /**
     * A charging name list def id (similar to RateCardEntry's matchingCategoryTypeId)
     * Specifies the type/category of the rate card used to match with matchingCategoryTypeId.
     */
    @Column
    Integer chargeNameId;

    @OneToMany(mappedBy = "rateCard")
    Set<RateCardEntry> rateCardEntries;

    @ManyToMany(fetch=FetchType.LAZY, mappedBy = "rateCards")
    List<Contract> contracts;

    @Enumerated(EnumType.STRING)
    @Column
    PartsOfWeek matchingPartsOfWeek;

//    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentLocalTime")
    @Column(columnDefinition = "TIME")
    LocalTime matchingStartTime;

    @Column(columnDefinition = "TIME")
    LocalTime matchingEndTime;

}
