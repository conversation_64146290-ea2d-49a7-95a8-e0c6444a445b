package com.ecco.finance.webApi.dto;

import static com.ecco.infrastructure.util.EccoTimeUtils.LONDON;

import com.ecco.buildings.dom.FixedContainer;
import com.ecco.buildings.repositories.FixedContainerRepository;
import com.ecco.dom.contracts.RateCard;
import com.ecco.dom.contracts.RateCardCalculation;
import com.ecco.contracts.ratecards.RateCardCalculationWithMonths;
import com.ecco.dom.contracts.RateCardEntry;
import com.google.common.collect.Range;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class FinanceChargeCalculation {

    private final FixedContainerRepository buildingRepository;

    public enum ChargeReason {MOVE_IN, MOVE_OUT, RATE, REPORT_END}

    public record ChargeChange(
            Instant date,
            ChargeReason reason,
            String description,
            Integer serviceRecipientId, // charge against, eg client
            Integer buildingId,
            RateCard rateCard
    ) {
        public static ChargeChange buildCharge(@NonNull Instant date, @NonNull ChargeReason reason, @NonNull String description, @NonNull Integer srId, @NonNull Integer bldgId) {
            return new ChargeChange(date, reason, description, srId, bldgId, null);
        }
        private static ChargeChange buildRateCard(@NonNull Instant date, @NonNull String description, @NonNull RateCard rateCard) {
            return new ChargeChange(date, ChargeReason.RATE, description, null, null, rateCard);
        }
        private static ChargeChange buildReportEnd(@NonNull Instant date) {
            return new ChargeChange(date, ChargeReason.REPORT_END, null, null, null, null);
        }
    }

    /**
     * Calculate the lines of the charges.
     * Starts from the first chargeable point (which is assumed to be data loaded around the from date)
     * and ends at the report end date (otherwise there might be no end - could possibly do financial year etc)
     */
    public List<ClientSalesChargeInvoiceDetailResource.Line> calculateLines(Range<Instant> reportRange, List<FinanceChargeCalculation.ChargeChange> chargeChanges, List<RateCard> rateCards) {

        assert reportRange.hasUpperBound(); // enforcing a report range makes logic easier
        // get all rate cards applicable for this period (including rate cards that follow on)
        // enforcing the calling method provides rate cards of the same type
        Assert.state(rateCards.stream().map(RateCard::getChargeNameId).distinct().count() == 1, () -> "rateCards in one calculation must be of the same charge name");

        RateCard currentRateCard = null;
        ChargeChange currentCharge = null;
        final RateCardCalculation calculator = new RateCardCalculationWithMonths();

        // multiple rate cards are sequential in time (of the same calculation/chargeName)
        var rateCardsInRange = calculator.getRateCardsInDateRange(reportRange, rateCards);
        var chargeChangeRateCards = rateCardsInRange.stream()
                .map(r -> ChargeChange.buildRateCard(r.getStartInstant(), "Rate changed to: " + r.getId(), r));

        // we add the reportRange as a chargeChange item but this does mean it cuts-up the charges exactly to the date
        // however, we only add the end date because that is required else things could go to infinity
        // whereas we just include the nearest charge around the report start date without cutting it up
        var chargeChangeReportEnd = Stream.of(ChargeChange.buildReportEnd(reportRange.upperEndpoint()));

        // put the charges together in order
        var chargeChangeAll = Stream.concat(chargeChanges.stream(), Stream.concat(chargeChangeRateCards, chargeChangeReportEnd))
                .sorted(Comparator.comparing(ChargeChange::date))
                .toList();

        // process the charges
        List<ClientSalesChargeInvoiceDetailResource.Line> lines = new ArrayList<>();
        var lastIndex = chargeChangeAll.size() - 1;
        for (int i = 0; i < chargeChangeAll.size(); i++) {
            var current = chargeChangeAll.get(i);
            // next is the move out or a new rate card or the report end date
            // in fact, we prioritise the report date if it comes up
            var next = current.reason.equals(ChargeReason.REPORT_END)
                    ? null
                    : (i+1) <= lastIndex
                    ? chargeChangeAll.get(i+1)
                    : null;

            currentCharge = current.reason.equals(ChargeReason.MOVE_IN)
                    ? current
                    : current.reason.equals(ChargeReason.MOVE_OUT)
                    ? null
                    : currentCharge;
            currentRateCard = current.reason.equals(ChargeReason.RATE) ? current.rateCard : currentRateCard;

            // to do a charge, we need to not be at the last item, and have a rate card (which could be really far in the past) and a move in available now
            if (next != null && currentRateCard != null && currentCharge != null) {
                var chargeFrom = current.date;
                var chargeTo = next.date != null ? next.date : null;
                var buildingChargeCategoryId = findChargeCategoryForBuilding(current.buildingId, currentRateCard.getRateCardEntries());

                Assert.state(buildingChargeCategoryId != null, () ->  "buildingId " + current.buildingId + " has no charge category");

                // TODO: This needs to not make assumptions on what rate card entries match or do conversions
                var entries = calculator.determineRateCardEntries(currentRateCard, null, buildingChargeCategoryId, null);

                // Create a ZonedDateTime range for the charge period - TODO: Clarify if we want UTC or LONDON as the zone
                if (chargeTo != null) {
                    ZonedDateTime start = chargeFrom.atZone(LONDON);
                    ZonedDateTime end = chargeTo.atZone(LONDON);
                    Assert.state(!start.isAfter(end), "start must not be after end");
                    Range<ZonedDateTime> dateTimeRange = Range.closedOpen(start, end);
                    var charge = calculator.calculateCharge(entries, dateTimeRange);

                    var line = new ClientSalesChargeInvoiceDetailResource.Line(currentCharge.serviceRecipientId,
                            UUID.randomUUID(), null, current.description, currentRateCard.getId(), charge, BigDecimal.valueOf(20), null, false,
                            current.buildingId, LocalDateTime.ofInstant(chargeFrom, LONDON), LocalDateTime.ofInstant(chargeTo, LONDON));
                    lines.add(line);
                }
            }
        }

        return lines;
    }

    private @Nullable Integer findChargeCategoryForBuilding(Integer buildingId, Set<RateCardEntry> rateCardEntries) {
        return buildingRepository.findById(buildingId)
                .map(FixedContainer::getNearestChargeCategoryId)
                .orElse(null);
    }
}
