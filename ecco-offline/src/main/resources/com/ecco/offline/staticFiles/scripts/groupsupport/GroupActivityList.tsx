import * as React from "react";
import {FC, useMemo, useState} from "react";
import ControlWrapper from "../components/ControlWrapper";
import {URLSearchParamsExtended, useAppBarOptions, useQuery, useServicesContext} from "ecco-components";
import {createDropdownList, SelectList} from "ecco-components-core";
import {useHistory} from "react-router";
import {History} from "history"
import { Grid, ButtonGroup, Button } from "@eccosolutions/ecco-mui";
import {
    useRestrictedServiceCategorisations,
    useRestrictedServices
} from "../entity-restrictions/entityRestrictionHooks";
import {GroupActivityPopupButton} from "./GroupSupportActivityEdit";
import {
    GROUPSUPPORTACTIVITY_LISTNAME,
    listDefToIdName,
    ServiceCategorisation,
    ServiceDto,
    SessionData,
    VENUE_LISTNAME
} from "ecco-dto";
import GroupActivityListControl from "./GroupActivityListControl";
import {GroupPageType} from "ecco-dto/group-support/GroupSupportRepository";

function navToQuery(history: History, activityTypeId: number | null, venueId: number | null,
                    serviceId: number | null, svcCatId: number | null, pageNum: number | null) {
    const params = new URLSearchParamsExtended()
    params.addNumberIfExists("venueId", venueId)
    params.addNumberIfExists("activityTypeId", activityTypeId)
    params.addNumberIfExists("serviceId", serviceId)
    params.addNumberIfExists("svcCatId", svcCatId)
    if (pageNum != 1) {
        params.addNumberIfExists("page", pageNum)
    }
    history.push(`?${params}`);
}

type GroupType = "GroupSupport" | "GroupComms" | "GroupAux";

export interface GroupActivityOptions {
    discriminator_orm: string,
    title: string,
    hasActivityType: boolean,
    hasVenue: boolean,
    hasDuration: boolean,
    hasCapacity: boolean,
    allowAttended: boolean,
    addService: boolean,
    activityListName: string,
    hasCategory: boolean,
    hasReviewDate: boolean,
    createListControl: (groupPageType: GroupPageType, activityTypeId: number, venueId: number, serviceId: number, svcCatId: number) => GroupActivityListControl
}

// see ControlWrapper - we could pass in a control
/*type Control = LoadableControl & {new(args:any): LoadableControl};
interface ControlFactory<C extends Control> {
    new(args: ConstructorParameters<C>);
}*/

const ListSizeSwitch = 15;
const activityTypeList = (sessionData: SessionData, activityTypeId: number, history: History,
                          venueId: number, serviceId: number, svcCatId: number, pageNum: number) => {
    const activityTypesListDefs = sessionData.getListDefinitionEntriesByListName(GROUPSUPPORTACTIVITY_LISTNAME, undefined, activityTypeId);

    const activityTypesListDefsCount = activityTypesListDefs.filter(ld => !ld.getDisabled()).length;
    if (activityTypesListDefsCount > ListSizeSwitch) {
        const activityTypes = activityTypesListDefs
                .filter(ld => !ld.getDisabled())
                .map(ld => {
                    return {
                        value: ld.getId().toString(),
                        label: ld.getDisplayName()
                    };
                });
        const activitySelectedFound = activityTypes.find(a => parseInt(a.value) === activityTypeId);
        return <SelectList createNew={false}
                           options={activityTypes}
                           value={activitySelectedFound}
                           placeholder={"activity"}
                           onChange={(o: {
                               value: string,
                               label: string
                           }) => navToQuery(history, o?.value ? parseInt(o.value) : null, venueId, serviceId, svcCatId, pageNum)} />;
    } else {
        const activityTypes = activityTypesListDefs.map(ld => listDefToIdName(ld));
        return createDropdownList(
                "activityType", "activity", activityTypeId,
                activityTypeId => navToQuery(history, activityTypeId as number, venueId, serviceId, svcCatId, pageNum),
                activityTypes);
    }
};

function venueList(sessionData: SessionData, venueId: number, history: History, activityTypeId: number, serviceId: number, svcCatId: number, pageNum: number) {
    const venuesListDefs = sessionData.getListDefinitionEntriesByListName(VENUE_LISTNAME, undefined, venueId);

    const venuesListDefsCount = venuesListDefs.filter(ld => !ld.getDisabled()).length;
    if (venuesListDefsCount > ListSizeSwitch) {
        const venues = venuesListDefs
                .filter(ld => !ld.getDisabled())
                .map(ld => {
                    return {
                        value: ld.getId().toString(),
                        label: ld.getDisplayName()
                    };
                });
        const venueSelectedFound = venues.find(a => parseInt(a.value) === venueId);
        return <SelectList createNew={false}
                           options={venues}
                           value={venueSelectedFound}
                           placeholder={"venue"}
                           onChange={(o: {
                               value: string,
                               label: string
                           }) => navToQuery(history, activityTypeId, o?.value ? parseInt(o.value) : null, serviceId, svcCatId, pageNum)} />;
    } else {
        const venues = venuesListDefs.map(ld => listDefToIdName(ld));
        return createDropdownList(
                "venue", "venue", venueId,
                venueId => navToQuery(history, activityTypeId, venueId as number, serviceId, svcCatId, pageNum),
                venues);
    }
}

function serviceList(services: ServiceDto[], venueId: number, history: History, activityTypeId: number, serviceId: number, pageNum: number) {

    const servicesListCount = services.filter(s => !s.disabled).length;
    if (servicesListCount > ListSizeSwitch) {
        const servicesList = services
                .filter(s => !s.disabled)
                .map(s => {
                    return {
                        value: s.id.toString(),
                        label: s.name
                    };
                });
        const servicesSelectedFound = servicesList.find(s => parseInt(s.value) === serviceId);
        return <SelectList createNew={false}
                           options={servicesList}
                           value={servicesSelectedFound}
                           placeholder={"service"}
                           onChange={(o: {
                               value: string,
                               label: string
                           }) => navToQuery(history, activityTypeId, venueId, o?.value ? parseInt(o.value) : null, null, pageNum)} />;
    } else {
        return createDropdownList(
                "service", "service", serviceId,
                serviceId => navToQuery(history, activityTypeId, venueId, serviceId as number, null, pageNum),
                services);
    }
}

// see also ServiceCategorisationSelect (which can be used on the referrals list by status)
function serviceCategoryList(sessionData: SessionData, serviceCats: ServiceCategorisation[], venueId: number, history: History, activityTypeId: number, svcCatId: number, pageNum: number) {

    const serviceCatsListCount = serviceCats.filter(s => !s.disabled).length;
    if (serviceCatsListCount > ListSizeSwitch) {
        const serviceCatsList = serviceCats
                .filter(s => !s.disabled)
                .map(s => {
                    return {
                        value: s.id.toString(),
                        label: sessionData.getServiceCategorisationName(s.id)
                    };
                });
        const serviceCatsSelectedFound = serviceCatsList.find(s => parseInt(s.value) === svcCatId);
        return <SelectList createNew={false}
                           options={serviceCatsList}
                           value={serviceCatsSelectedFound}
                           placeholder={"service"}
                           onChange={(o: {
                               value: string,
                               label: string
                           }) => navToQuery(history, activityTypeId, venueId, null, o?.value ? parseInt(o.value) : null, pageNum)} />;
    } else {
        const serviceCatsList = serviceCats
                .filter(s => !s.disabled)
                .map(s => {
                    return {
                        id: s.id,
                        name: sessionData.getServiceCategorisationName(s.id)
                    };
                });
        return createDropdownList(
                "service", "service", svcCatId,
                svcCatId => navToQuery(history, activityTypeId, venueId, null, svcCatId as number, pageNum),
                serviceCatsList);
    }
}

export const GroupActivityList: FC<{options: GroupActivityOptions}> = ({options}) => {
    const query = useQuery()
    const groupPageTypeIn = query.get("groupPageType") as GroupPageType
    const activityTypeId = query.getInt("activityTypeId")
    const venueId = query.getInt("venueId")
    const serviceId = query.getInt("serviceId")
    const svcCatId = query.getInt("svcCatId")

    const pageNumIn = query.getInt("page") || 1
    const history = useHistory();
    useAppBarOptions(options.title);

    const {sessionData} = useServicesContext();
    const {services} = useRestrictedServices();
    const {serviceCats} = useRestrictedServiceCategorisations();

    const [pageTypeSelector, setPageTypeSelector] = useState<GroupPageType>(groupPageTypeIn || "courses");
    const [pageNum, setPageNum] = useState<number>(pageNumIn);

    const changePageType = (type: GroupPageType) => {
        setPageTypeSelector(type);
        setPageNum(1);
    }

    const control = useMemo(() => {
        return options.createListControl(groupPageTypeIn, activityTypeId, venueId, serviceId, svcCatId);
    }, []);

    if (!sessionData || !services || !serviceCats) {
        return null; // Could be loading spinner
    }

    //const hasServices = sessionData.isEnabled("groupsupport.list.column.service");
    //const hasProjects = sessionData.isEnabled("groupsupport.list.column.project");
    const hasSvcCat = sessionData.isEnabled("groupsupport.list.column.svcCat");
    // NB this is far too much code just to switch select lists
    let ServicesList = serviceList(services, venueId, history, activityTypeId, serviceId, pageNum);
    let ServiceCatsList = serviceCategoryList(sessionData, serviceCats, venueId, history, activityTypeId, svcCatId, pageNum);
    let ActivityTypesList = activityTypeList(sessionData, activityTypeId, history, venueId, serviceId, svcCatId, pageNum);
    let VenueList = venueList(sessionData, venueId, history, activityTypeId, serviceId, svcCatId, pageNum);

    control.update(pageTypeSelector, activityTypeId, venueId, serviceId, svcCatId, pageNum)

    const HistoryPlannedSelector = (
        <Grid item>
            <ButtonGroup size="small" aria-label="switch task">
                <Button onClick={() => changePageType("courses")}
                        /*disabled={loadSelector == "planned" ? undefined : true}*/
                        color={pageTypeSelector == "courses" ? "primary" : undefined}
                >
                    courses
                </Button>
                <Button onClick={() => changePageType("sessions")}
                        /*disabled={loadSelector == "historical" ? undefined : true}*/
                        color={pageTypeSelector == "sessions" ? "primary" : undefined}
                >
                    sessions
                </Button>
            </ButtonGroup>
        </Grid>
    );

    return <Grid container justify="center">
        <Grid item xs={12} style={{textAlign: "center"}}>
            {HistoryPlannedSelector}
        </Grid>

        <Grid item xs={12} style={{textAlign: "right"}}>
            <GroupActivityPopupButton options={options} type={pageTypeSelector} activityId={null} parentId={null} reload={() => {
                control.update(pageTypeSelector, activityTypeId, venueId, serviceId, svcCatId, pageNum);
            }} />
        </Grid>
        <Grid item md={4} sm={6} xs={12}>
            {hasSvcCat
                ? ServiceCatsList
                : ServicesList}
        </Grid>
        {options.hasActivityType &&
            <Grid item md={4} sm={6} xs={12}>
                {ActivityTypesList}
            </Grid>
        }
        {options.hasVenue &&
            <Grid item md={4} sm={6} xs={12}>
                {VenueList}
            </Grid>
        }
        <Grid item xs={12}>
            <ControlWrapper control={control}/>
        </Grid>
    </Grid>;

}