import EntityRestrictions = require("../entity-restrictions/EntityRestrictions");
import $ = require("jquery");
import SelectList = require("../controls/SelectList");
import {EccoDateTime} from "@eccosolutions/ecco-common";
import {apiClient} from "ecco-components";

import * as gsDto from "ecco-dto/group-support-dto";
import {EntityRestrictionsAjaxRepository} from "../entity-restrictions/EntityRestrictionsAjaxRepository";
import {GroupSupportAjaxRepository} from "ecco-dto";
import {SessionData, SessionDataAjaxRepository} from "ecco-dto";
import * as dto from "ecco-dto/group-support-dto";
import {getFeatureConfigRepository} from "ecco-offline-data";

const sessionDataRepository = new SessionDataAjaxRepository(apiClient);
const entityRestrictionsRepository = new EntityRestrictionsAjaxRepository(apiClient);
const groupSupportAjaxRepository = new GroupSupportAjaxRepository(apiClient);

/**
 * The class which sets up the dashboards and controls for registered interest
 * UNUSED
 */
class GroupSupportInterestView {

    private restrictions: EntityRestrictions;
    private activityTypes = new SelectList("groupSupportActivityType");
    private initialTypeValue: string;

    public init() {
        this.createDropDowns();
    }

    private createDropDowns(): void {

        this.restrictions = new EntityRestrictions(entityRestrictionsRepository, sessionDataRepository,
                (source) => this.updateFormForSelectedServiceAndProject());

        this.restrictions.attachTo($("#servicesProjects"), "service", "project");

        const $activityTypes = $("#groupSupportActivityTypes");
        this.initialTypeValue = $activityTypes.attr("data-initial-value");

        const $row = $("<div>").attr("class", "e-row")
            .append($("<span>").attr("class", "e-label").text("activity type "))
            .append($("<span>").attr("class", "input").append(this.activityTypes.element()));

        $activityTypes.append($row);
    }

    private updateFormForSelectedServiceAndProject(): void {
        const serviceId = this.restrictions.getSelectedService();
        if (serviceId && serviceId != -1) {
            const activityTypes = groupSupportAjaxRepository.findActivityTypesByServiceId(serviceId);
            const activities = groupSupportAjaxRepository.findNextScheduledActivitiesByServiceId(serviceId);
            const sdQ = getFeatureConfigRepository().getSessionData();

            Promise.all([sdQ, activityTypes, activities])
                .then(([sd, activityTypes, nextActivities]) => {
                    this.updateActivityTypesOptions(sd, activityTypes, nextActivities);
                });
        }
    }

    private updateActivityTypesOptions(sessionData: SessionData, activityTypes: gsDto.ActivityTypeDto[], nextActivities: gsDto.GroupActivityDto[]) {
        const typesMinusScheduled = activityTypes.filter((type) => {

            for (let i = 0; i < nextActivities.length; i++) {
                if (nextActivities[i].activityTypeId == type.id) {
                    // TODO we've orphaned this table, so this is useless currently
                    //nextActivities[i].activityType.numReferrals = type.numReferrals; // cos it wasn't provided as part of this entity (HACK)
                    return false;
                }
            }
            return true;
            // could be as follows if we got demand into nextActivities: return !nextActivities.some( (activity) => activity.activityType.id == type.id )
        });

        const noDemand = typesMinusScheduled.filter((type) => type.numReferrals == 0);
        const hasDemand = typesMinusScheduled.filter((type) => type.numReferrals != 0);

        this.activityTypes.clear();
        hasDemand.length > 0 && this.activityTypes.addOptionGroupFromList("no future events scheduled", hasDemand,
            (idName) => this.entryForActivityType(idName),
            (idName) => idName.id.toString() == this.initialTypeValue);

        nextActivities.length > 0 && this.activityTypes.addOptionGroupFromList("events already scheduled", nextActivities,
            (activity) => this.entryForActivity(activity, sessionData),
            (idName) => idName.id.toString() == this.initialTypeValue);

        if (noDemand.length == activityTypes.length) {
            this.activityTypes.populateFromList(activityTypes,
                (idName) => this.entryForActivityType(idName),
                (idName) => idName.id.toString() == this.initialTypeValue);
        } else {
            noDemand.length > 0 && this.activityTypes.addOptionGroupFromList("no interest at present", noDemand,
                (idName) => this.entryForActivityType(idName),
                (idName) => idName.id.toString() == this.initialTypeValue);
        }
    }

    private entryForActivityType(idName: gsDto.ActivityTypeDto) {
        return { key: idName.id.toString(),
            value: idName.name + ((idName.numReferrals > 0) ? " (interest: " + idName.numReferrals + ")" : "")};
    }

    private entryForActivity(activity: dto.GroupActivityDto, sessionData: SessionData) {
        const label = this.annotateWithDemand(sessionData.getListDefinitionEntryById(activity.activityTypeId)?.getDisplayName(),
                EccoDateTime.parseIso8601IgnoringTimezone(activity.startDateTime),
                999, activity.capacity, 0);// activity.activityType.numReferrals);

        return { key: activity.activityTypeId.toString(), value: label};
    }

    private annotateWithDemand(name: string, nextScheduled: EccoDateTime, placesTaken: number, placesAvail: number,
        activityDemand: number): string {

        if (!nextScheduled) {
            return name + " (interest: " + activityDemand + ")";
        }
        return name + " ( " + nextScheduled.formatDatePretty() + ": " + /*placesTaken + "/" +*/ placesAvail
            + " places, interest: " + activityDemand + ")";
    }
}


const gs = new GroupSupportInterestView();
gs.init();
