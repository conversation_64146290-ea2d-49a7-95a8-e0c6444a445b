import * as commonDto from "@eccosolutions/ecco-common";
import {WebApiError} from "@eccosolutions/ecco-common";
import {apiClient} from "ecco-components";
import {showInModalDom} from "ecco-components-core";
import "jquery-file-upload";
import $ = require("jquery");

import JQueryFileUploadAdd = $.JQueryFileUploadAdd;
import JQueryFileUploadDone = $.JQueryFileUploadDone;
import JQueryFileUploadFilesObject = $.JQueryFileUploadFilesObject;
import JQueryFileUploadProgressObject = $.JQueryFileUploadProgressObject;
import UploadResult = commonDto.UploadResult;
import {ApiClient} from "ecco-dto";

/*
 * NOTE: Load attachmentsInit from JS to bootstrap this.
 */


/** Find the different items we support and attach the appropriate control to each of them */
export function attachViaDataAttrs() {
    const $roots = $(".attachment-list");
    $roots.each( (index, elem) => {
        addControl($(elem));
    });
}

function addControl($container: $.JQuery) {
    const attachmentsControl = new AttachmentsControl($container.data("uploadHref"),
        $container.data("unattachedListHref"), $container.data("attachedListHref"),
        $container.data("targetInput"), Number($container.data("maxSize")),
        Boolean($container.data("readOnly")));
    $container.append(attachmentsControl.element());
}


export class AttachmentsControl {
    private readonly $container: $.JQuery;
    private $fileUpload: $.JQuery;
    private attachments: UploadResult[] = [];
    private apiClient: ApiClient;

    /**
     * targetInput
     *  where to record the fileId that have been uploaded and should be attached with this work (e.g. "#attachmentFileId")
     */
    constructor(private uploadHref: string, unattachedHref: string, attachedHref: string,
            private targetInput: string, private maxSize: number, readOnly: boolean) {

        // TODO: client side max size check (see 'beforeSend' in comments below).

        this.apiClient = apiClient;
        if (attachedHref) this.loadAttachedFiles(attachedHref);

        this.$fileUpload = $("<input>");

        this.$container = $("<div>")
                .addClass("attachments-ctl")
                .append($("<button>")
                        .attr("type", "button")
                        .addClass("btn btn-default attachments-btn-attach")
                        .append("+&nbsp;")
                        .append($("<i>")
                                .addClass("glyphicon glyphicon-paperclip"))
                        .append($("<i>")
                                .addClass("sr-only")
                                .text("Attach File"))
                        .click(this.onAttachFileButtonClick))
                .append(AttachmentsControl.createAttachedContainer())
                .append($("<div>")
                        .addClass("attachments-progress progress hidden")
                        .append($("<div>")
                                .addClass("attachments-progress-bar progress-bar progress-bar-striped active")
                                .attr("role", "progressbar")
                                .attr("aria-valuemin", "0")
                                .attr("aria-valuemax", "100")))
                .append(this.$fileUpload);

        if (readOnly) {
            this.$fileUpload.hide();
            this.$container.find(".attachments-btn-attach").hide();
        } else {
            if (unattachedHref) this.loadUnattachedFiles(unattachedHref);

            this.newJQueryFileUpload();

            if (targetInput) {
                $(this.targetInput).closest("form").submit( (e) => { this.checkTargetFormSubmit(e); });
            }
        }

    }

    public static createAttachedContainer(): $.JQuery {
        return $("<div>")
            .addClass("dropdown attachments-attached hidden")
            .append($("<button>")
                    .attr("type", "button")
                    .addClass("btn btn-link dropdown-toggle")
                    .attr("data-toggle", "dropdown")
                    .append($("<span>")
                            .addClass("attachments-attached-title"))
                    .append($("<span>")
                            .addClass("caret")))
            .append($("<ul>")
                    .addClass("dropdown-menu")
                    .attr("role", "menu")
                    .append($("<li>")
                            .addClass("dropdown-header attachments-attached-pending")
                            .attr("role", "presentation")
                            .text("uploading"))
                    .append($("<li>")
                            .addClass("dropdown-header attachments-attached-new")
                            .attr("role", "presentation")
                            .text("new attachments"))
                    .append($("<li>")
                            .addClass("dropdown-header attachments-attached-old")
                            .attr("role", "presentation")
                            .text("existing attachments"))
                    .append($("<li>")
                            .addClass("dropdown-header attachments-attached-failed")
                            .attr("role", "presentation")
                            .text("failed uploads")));
    }

    private onAttachFileButtonClick = (event: $.JQueryMouseEventObject) => {
        // Stupid hack:
        // When this attribute is set, clicking the upload button displays
        // a dropdown list of previously uploaded files. Therefore, we don't
        // want to activate the file upload dialog if the user clicks on a
        // button with this attribute set.
        if ($(event.target).closest('[data-toggle=dropdown]').length == 0) {
            // Activate the file upload dialog.
            this.$fileUpload.click();
        }
    };

    // Load a list of files which are available for attaching but already uploaded
    private loadUnattachedFiles(unattachedHref: string) {
        this.apiClient.get<UploadResult[]>(unattachedHref)
                .then((data: UploadResult[]) => { this.unattachedFilesLoaded(data) })
                .catch((error: any) => { this.nonFatalError("Unattached files could not be loaded", error) });
    }

    private unattachedFilesLoaded(data: UploadResult[]) {
        // Attaches a dropdown menu with a list of previously uploaded files.
        function attachMenu() {
            $dropdown.insertAfter($button);
            $button.attr("data-toggle", "dropdown");
        }

        // Detaches the dropdown menu.
        function detachMenu() {
            $button.removeAttr("data-toggle");
            $dropdown.remove();
        }

        if (data.length > 0) {
            var $button = this.$container.find(".attachments-btn-attach");
            var $dropdown: $.JQuery = this.createUnattachedMenu(data, detachMenu);

            $("<li>").addClass("divider").appendTo($dropdown);
            const $browseOption = $("<li>").addClass("attachments-btn-attach").attr("role", "presentation").appendTo($dropdown);
            $browseOption.append($("<a>")
                    .attr("role", "menuitem")
                    .text("upload new file\u2026")
                    .click(this.onAttachFileButtonClick));

            attachMenu();

        }
    }

    private createUnattachedMenu(files: UploadResult[], removeDropdownFn: () => any):$.JQuery {
        const $dropdown: $.JQuery = $("<ul>").addClass("dropdown-menu").attr("role", "menu");
        $("<li>").addClass("dropdown-header").attr("role", "presentation").text("previously uploaded").appendTo($dropdown);
        for (let i = 0; i < files.length; i++) {
            this.createUnattachedMenuItem(files[i], removeDropdownFn).appendTo($dropdown);
        }
        return $dropdown;
    }

    private createUnattachedMenuItem(uploadedFile: UploadResult, removeDropdownFn: () => any):$.JQuery {
        const $menuItem = $("<li>").attr("role", "presentation").addClass("attachments-unattached-file");

        return $menuItem.append(
            $("<a>").attr("role", "menuitem").text(uploadedFile.filename).click(() => {
                this.attachUploadedFile(uploadedFile);

                if ($menuItem.siblings(".attachments-unattached-file").length == 0) {
                    // This was the last unattached file, so reinstate the straight-to-file-selector behaviour.
                    removeDropdownFn();
                } else {
                    $menuItem.remove();
                }
            })
        );
    }

    // Load a list of files which are already attached
    private loadAttachedFiles(attachedHref: string) {
        this.apiClient.get<UploadResult[]>(attachedHref)
                .then((data: UploadResult[]) => { AttachmentsControl.attachedFilesLoaded(this.$container, data) })
                .catch((error: any) => { this.nonFatalError("Attached files could not be loaded", error) });
    }

    public static attachedFilesLoaded($control: $.JQuery, data: UploadResult[], inline = false) {
        if (data.length > 0) {
            AttachmentsControl.createAttachedMenuItems(data).insertAfter($control.find(".attachments-attached-old"));
            AttachmentsControl.updateAttachedListTitles($control);
            if (inline) {
                $control.find("div.attachments-attached > ul.dropdown-menu").removeClass("dropdown-menu");
                $control.find("div.attachments-attached > button.dropdown-toggle").hide();
            }
        }
    }

    private newJQueryFileUpload():void {
        const $fileUpload = $("<input>")
            .attr("type", "file")
            .attr("multiple", "multiple")
            .css({
                position: "absolute",
                left: "-10000px",
                margin: 0,
                border: 0,
                padding: 0,
                width: 0,
                height: 0,
                opacity: 0
            })

        $fileUpload
                .fileupload({
                    url: this.uploadHref,
                    timeout: 3600000,
                    maxChunkSize: 10240000, /* Larger files than this may result in multiple partial attachments or maxPacketSize issues at database */
                    dataType: 'json',
                    dropZone: this.$container,
                    pasteZone: this.$container
                })
                .bind<JQueryFileUploadDone>('fileuploaddone', (e, data) => this.uploadComplete(e, data))
                .bind<JQueryFileUploadFilesObject>('fileuploadfail', (e, data) => this.uploadFailed(e, data))
                .bind('fileuploadprogressall', (e, data) => this.progressUpdate(e, data))
                .bind('fileuploadstart', (e) => this.uploadsStarting(e))
                .bind('fileuploadstop', (e) => this.allUploadsComplete(e))
                .bind<JQueryFileUploadAdd>('fileuploadadd', (e, data) => this.uploadInitiated(e, data));

        this.$fileUpload.replaceWith($fileUpload);
        this.$fileUpload = $fileUpload;
    }

    public uploadInitiated(e: $.JQueryEventObject, data: JQueryFileUploadAdd) {
        if (data && data.files) {
            for (let file of data.files) {
                if (file.size > this.maxSize) {
                    alert(`Upload rejected. Maximum file size is ${this.maxSize} bytes. `);
                    throw new Error("File too big");
                }
            }
            // Create pending menu items.
            const $items: $.JQuery = this.createAttachedMenuItemsFromFiles(data.files).insertAfter(this.$container.find(".attachments-attached-pending"));
            AttachmentsControl.updateAttachedListTitles(this.$container);

            // Process the data and submit it. When the submit is done, remove the pending menu items.
            data.process().then(() => {
                data.submit().always((result, textStatus, jqXHR) => {
                    $items.remove();
                    AttachmentsControl.updateAttachedListTitles(this.$container);
                });
            });
        }
    }

    public uploadComplete(e: $.JQueryEventObject, data: JQueryFileUploadDone) {
        if (data && data.result) {
            if (data.result.error) {
                if (data.result.filename) {
                    this.attachmentErrored(data.result);
                } else {
                    const fileUploadFailData: JQueryFileUploadFilesObject = {
                        files: (<any>data).files,
                        errorThrown: data.result.error
                    };
                    this.uploadFailed(undefined, fileUploadFailData);
                }
            } else {
                this.attachUploadedFile(data.result);
            }
        }

        // The old input element is no longer JQFU-enhanced, so create a new one.
        this.newJQueryFileUpload();
    }

    private attachUploadedFile(uploadResult: UploadResult) {
        const indexMatch = /\[\d+\]/;
        let $cloneSource;
        let $cloned;
        let inputName;

        if (uploadResult.fileId) {
            if (this.targetInput) {
                const $targetInputs: $.JQuery = $(this.targetInput);
                if ($targetInputs.length == 1 && !$targetInputs.val()) {
                    $targetInputs.val(uploadResult.fileId);
                } else {
                    $cloneSource = $targetInputs.last();
                    $cloned = $cloneSource.clone().removeAttr("id").insertAfter($cloneSource).val(uploadResult.fileId);
                    inputName = $cloned.attr("name");
                    // If name contains an index in square brackets, then make sure it is correct.
                    if (inputName.match(indexMatch)) {
                        $cloned.attr("name", inputName.replace(indexMatch, "[" + $cloned.siblings("input").length + "]"));
                    }
                }
            }

            this.attachments.push(uploadResult);
            this.attachmentAdded(uploadResult);
        }
    }

    private attachmentAdded(file: UploadResult) {
        AttachmentsControl.createAttachedMenuItem(file).insertAfter(this.$container.find(".attachments-attached-new"));
        AttachmentsControl.updateAttachedListTitles(this.$container);
    }

    private static updateAttachedListTitles($control: $.JQuery) {
        const $attached = $control.find(".attachments-attached");

        // Overall dropdown title
        const count = $attached.find(".attachments-attached-file").length; // All file menu items get this class.
        if (count >= 1) {
            $attached.removeClass("hidden");
        }
        $attached.find(".attachments-attached-title").text(String(count) + " attachment" + (count != 1? "s\u00a0" : "\u00a0"));

        // Hide any empty sections - shame CSS can't do this.
        $attached.find(".dropdown-header.hidden").removeClass("hidden");
        $attached.find(".dropdown-header + .dropdown-header").prev().addClass("hidden");
        $attached.find(".dropdown-header").filter(":last-child").addClass("hidden");
    }

    public static createAttachedMenuItems(uploadedFiles: UploadResult[]):$.JQuery {
        return uploadedFiles
            .map(file => AttachmentsControl.createAttachedMenuItem(file))
            .reduce((selection, elements) => selection.add(elements));
    }

    private createAttachedMenuItemsFromFiles(files: File[]):$.JQuery {
        return files
            .map(file => this.html5FileToUploadResult(file))
            .map(uploadResult => AttachmentsControl.createAttachedMenuItem(uploadResult))
            .reduce((selection, elements) => selection.add(elements));
    }

    private html5FileToUploadResult(f: File, message?: string): UploadResult {
        return { filename: f.name, type: f.type, size: f.size, error: message, links: [] };
    }

    public static createAttachedMenuItem(uploadedFile: UploadResult):$.JQuery {
        const $menuItem = $("<li>").attr("role", "presentation").addClass("attachments-attached-file");
        const $link = $("<a>").attr("role", "menuitem")
            .append($("<div>").addClass("attachments-attached-file-info pull-right")
                .append($("<span>").css({'margin-left': '5px'}).addClass("attachments-attached-file-size small").text(AttachmentsControl.parseSize(uploadedFile.size)))
            )
            .append($("<span>").addClass("attachments-attached-file-name").text(uploadedFile.filename));

        if (uploadedFile.links) {
            $.each(uploadedFile.links, (index, link) => {
                if (link.rel && (link.rel == 'enclosure')) {
                    // href is the downloadHidden
                    $link.attr('href', link.href);
                    return true;
                }
                return false;
            });
        }

        return $menuItem.append($link);
    }

    public uploadFailed(e:$.JQueryEventObject, data: JQueryFileUploadFilesObject) {
        if (data && data.files) {
            const $items = this.createAttachedMenuItemsFromFiles(data.files).insertAfter(this.$container.find(".attachments-attached-failed"));
            this.markMenuItemsAsError($items, data.errorThrown || data.textStatus);
            AttachmentsControl.updateAttachedListTitles(this.$container);
        }

        // The old input element is no longer JQFU-enhanced, so create a new one.
        this.newJQueryFileUpload();
    }

    private markMenuItemsAsError($items:$.JQuery, message: string) {
        $items.removeClass("attachments-attached-file").addClass("bg-danger").find(".attachments-attached-file-info")
                .empty()
                .append("\u00a0")
                .append($("<span>").addClass("glyphicon glyphicon-exclamation-sign").attr("title", message));
    }

    private attachmentErrored(file: UploadResult) {
        const $item = AttachmentsControl.createAttachedMenuItem(file).insertAfter(this.$container.find(".attachments-attached-failed"));
        this.markMenuItemsAsError($item, file.error);
        AttachmentsControl.updateAttachedListTitles(this.$container);
    }

    public progressUpdate(e: $.JQueryEventObject, data: JQueryFileUploadProgressObject) {
        if (data) {
            this.updateCurrentProgress(data.loaded, data.total, data.bitrate);
        }
    }

    private updateCurrentProgress(loaded: number, total: number, bitrate: number) {
        const $progress = this.$container.find(".attachments-progress");
        const pc = Math.round(loaded / total * 100);
        $progress.find(".attachments-progress-bar").css("width", pc + '%').attr("aria-valuenow", String(pc)).text(pc + '%');
        $progress.attr("title", "loaded: " + AttachmentsControl.parseSize(loaded) + "; total: "
            + AttachmentsControl.parseSize(total) + "; bitrate: " + AttachmentsControl.parseSize(bitrate) + "/s");
    }

    public uploadsStarting(e:$.JQueryEventObject) {
        this.showCurrentProgress();
    }

    private checkTargetFormSubmit(e:$.JQueryEventObject) {
        // Don't allow the target form to submit if there are uploads in progress
        if (this.$fileUpload.fileupload('active')) {
            e.preventDefault();
            // TODO: improve UX e.g. show the upload progress in the modal dialog and automatically resubmit the form on completion.
            const content = document.createElement("p");
            content.textContent = "There are active uploads in progress. Please wait until they have completed.";
            showInModalDom("Uploads in progress", content);
        }
    }

    private showCurrentProgress() {
        this.$container.find(".attachments-progress").removeClass("hidden")
                .attr("title", "")
                .find(".attachments-progress-bar").css("width", "0%").attr("aria-valuenow", "0").text("0%");
    }

    public allUploadsComplete(e:$.JQueryEventObject) {
        this.hideCurrentProgress();
    }

    private hideCurrentProgress() {
        this.$container.find(".attachments-progress").addClass("hidden");
    }

    private nonFatalError(error: string, cause: WebApiError) {
        window.alert(error + ": " + (cause.reason && cause.reason.message || ("[" + cause.statusCode + "]")));
    }

    public element(): $.JQuery {
        return this.$container;
    }

    private static parseSize(size: number) {
        const suffix = ["b", "kb", "mb", "gb", "tb", "pb"];
        let tier = 0;
        while(size >= 1024) {
            size = size / 1024;
            tier++;
        }
        return Math.round(size * 10) / 10 + "" + suffix[tier];
    }
}
