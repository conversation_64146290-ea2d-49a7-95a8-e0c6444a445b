import {EccoDate, isEmpty} from "@eccosolutions/ecco-common";
import {CommandQueue} from "ecco-commands";
import {
    CommandForm,
    CommandSubform,
    EccoAPI,
    ModalCommandForm,
    SmallSpinner,
    useServicesContext
} from "ecco-components";
import {datePickerInput,
    dateTimeIso8601Input, dropdownList,
    datePickerIso8601Input,
    numberInput,
    textInput
} from "ecco-components-core";
import {default as React, FC, useState} from "react";
import {mountWithServices} from "../offline/ServicesContextProvider";
import {useGroupActivity} from "./groupsSupportHooks";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {Button, Grid} from "@eccosolutions/ecco-mui";
import {GroupActivity} from "ecco-dto";
import {GroupActivityCommand} from "./commands";
import {RestrictedServicesDropdown} from "../entity-restrictions/RestrictedServicesDropdown";
import {ProjectDropdown} from "../entity-restrictions/ProjectDropdown";
import {unCamelCase} from "ecco-reports";
import commands = require("./commands");
import {
    GroupActivityDto,
    GROUPAUXCATEGORY_LISTNAME,
    listDefToIdName,
    SessionData,
    VENUE_LISTNAME
} from "ecco-dto";
import {GroupActivityOptions} from "./GroupActivityList";
import {GroupPageType} from "ecco-dto/group-support/GroupSupportRepository";

export function groupActivity(options: GroupActivityOptions, activityId: number, parentId: number, el, reload: () => void) {
    mountWithServices(
        <GroupActivityPopupButton options={options} type={parentId ? "sessions" : null} parentId={parentId} activityId={activityId} reload={reload}/>,
        el || document.createElement("span"),
        <SmallSpinner/>
    );
}

export const GroupActivityPopupButton: FC<{options: GroupActivityOptions, type: GroupPageType | null, parentId: number, activityId: number, reload: () => void}> = ({options, type, activityId, parentId, reload}) => {
    const activityEdit = activityId && useGroupActivity(activityId).activity;
    const parent = parentId && useGroupActivity(parentId).activity;
    const [show, setShow] = useState(false);

    // clone if we are not editing, and there are parent/course details
    const clone = !!parent && !activityId;

    const templateActivity: GroupActivityDto | null = clone && {
        id: null,
        uuid: Uuid.randomV4().toString(), // have a reference to the activity (see GroupSupportAction.java)
        startDateTime: null,
        endDate: null,
        serviceRecipientId: null,
        course: false,
        parentId: parent.getDto().parentId,
        activityTypeId: parent.getDto().activityTypeId,
        description: parent.getDto().description,
        serviceId: parent.getDto().serviceId,
        projectId: parent.getDto().projectId,
        venueId: parent.getDto().venueId,
        capacity: parent.getDto().capacity,
        duration: parent.getDto().duration,
        links: []
    };

    const activity = !clone ? activityEdit : new GroupActivity(templateActivity);

    return <>
        {/*TODO: Split and move create to the titlebar as (+) FAB as for ad-hoc on the rota */}
        {activityId
            ? <a onClick={() => setShow(true)}>details</a>
            : <Button variant="outlined" color="primary" size="medium" onClick={() => setShow(true)}>
                {`new ${type == "sessions" ? "session" : "course"}`}
              </Button>
        }
        {show && (activity || !activityId) && <GroupActivityModal
            options={options}
            activity={activity}
            parentId={parentId}
            course={activity ? activity.getDto().course : type == "courses"}
            setShow={setShow}
            afterSave={reload}
        />}
    </>;
};

type LocalProps = { services: EccoAPI, commandForm: CommandForm };

interface ModalProps {
    options: GroupActivityOptions;
    setShow: (show: boolean) => void
    course: boolean;
    activity: GroupActivity | null;
    parentId: number;
    afterSave: () => void;
}

interface Props extends ModalProps {
    readOnly: boolean;
    sessionData: SessionData;
}

interface State {
    description: string | null
    serviceId: number | null
    projectId: number | null
    activityTypeId: number | null
    venueId: number | null
    capacity: number | null
    startDateTime: string | null // this is zoneless
    endDate: EccoDate | null
    duration: number | null,
    categoryId: number | null
}

export class GroupActivityEditCommandForm extends CommandSubform<Props & LocalProps, State> {

    constructor(props: Props & LocalProps) {
        super(props);
        const activity = props.activity;
        const dto = activity?.getDto();
        this.state = dto ? {
            description: dto.description,
            serviceId: dto.serviceId,
            projectId: dto.projectId,
            activityTypeId: dto.activityTypeId,
            venueId: dto.venueId,
            capacity: dto.capacity,
            startDateTime: dto.startDateTime,
            endDate: activity.getEndDate(),
            duration: dto.duration,
            categoryId: null
        }
        : {} as State
    }

    getErrors(): string[] {
        const requiredFields: (keyof State)[] = ['activityTypeId', 'description', 'startDateTime'];
        /*if (this.props.options.hasVenue) {
            requiredFields.push('venueId');
        }
        if (this.props.options.hasDuration) {
            requiredFields.push('duration');
        }*/
        return requiredFields.reduce( (errors, field) => {
            if (isEmpty(this.state[field])) {
                let desc = unCamelCase(field)
                desc = desc.endsWith(" id")
                    ? desc.substr(0, desc.length - 3)
                    : desc;
                errors.push(`${desc} is required`);
            }
            return errors;
        }, []);
    }

    emitChangesTo(commandQueue: CommandQueue) {

        let cmd;
        if (this.props.activity && this.props.activity.getDto().id) {
            const startDateTime = this.props.activity.getStartDateTime();
            cmd = new GroupActivityCommand("update", Uuid.parse(this.props.activity.getDto().uuid))
                .changeDescription(this.props.activity.getDto().description, this.state.description)
                .changeService(this.props.activity.getDto().serviceId, this.state.serviceId)
                .changeProject(this.props.activity.getDto().projectId, this.state.projectId)
                .changeActivityType(this.props.activity.getDto().activityTypeId, this.state.activityTypeId)
                .changeVenue(this.props.activity.getDto().venueId, this.state.venueId)
                .changeCapacity(this.props.activity.getDto().capacity, this.state.capacity)
                .changeDuration(this.props.activity.getDto().duration, this.state.duration)
                .changeStartDateTimeIso(startDateTime && startDateTime.formatIso8601(), this.state.startDateTime)
                .changeEndDate(this.props.activity.getEndDate(), this.state.endDate);
        } else {
            cmd = new commands.GroupActivityCommand("create", Uuid.parse(this.props.activity?.getDto()?.uuid) || Uuid.randomV4(), this.props.options.discriminator_orm, this.props.parentId)
                .changeDescription(null, this.state.description)
                .changeService(null, this.state.serviceId)
                .changeProject(null, this.state.projectId)
                .changeActivityType(null, this.state.activityTypeId)
                .changeVenue(null, this.state.venueId)
                .changeCapacity(null, this.state.capacity)
                .changeDuration(null, this.state.duration)
                .changeStartDateTimeIso(null, this.state.startDateTime)
                .changeEndDate(null, this.state.endDate)
            if (this.props.course) {
                cmd.asCourse();
            }
        }

        if (cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }
    }

    render() {
        const setter = state => this.setState(state);

        return <Grid container>
            <Grid item xs={12}>
                {dateTimeIso8601Input("startDateTime", "start date & time",
                                      setter, this.state, false, true)}
            </Grid>
            <Grid item xs={12}>
                {dropdownList(
                    "activity type",
                    setter,
                    this.state,
                    "activityTypeId",
                    this.props.sessionData.getListDefinitionEntriesByListName(this.props.options.activityListName, undefined, this.state.activityTypeId).map(ld => listDefToIdName(ld)),
                    {},
                    undefined,
                    true
                )}
            </Grid>
            <Grid item xs={12}>
                {textInput("description", "description",
                    setter, this.state, undefined, false, true)}
            </Grid>
            <Grid item xs={12}>
                <RestrictedServicesDropdown
                        label="service"
                        state={this.state}
                        setter={setter}
                        propertyKey="serviceId"
                />
            </Grid>
            <Grid item xs={12}>
                {this.state.serviceId &&
                        // ideally we save the serviceCategorisationId instead of projectId, so we can pass to check not disabled
                        <ProjectDropdown
                                serviceId={this.state.serviceId}
                                label="project"
                                state={this.state}
                                setter={setter}
                                propertyKey="projectId"
                        />}
            </Grid>
            {this.props.options.hasVenue && <Grid item xs={12}>
                {dropdownList(
                    "venue",
                    setter,
                    this.state,
                    "venueId",
                    this.props.sessionData.getListDefinitionEntriesByListName(VENUE_LISTNAME, undefined, this.state.venueId).map(ld => listDefToIdName(ld)),
                    {},
                    undefined,
                    false
                )}
            </Grid>}
            {this.props.course &&
                <Grid item xs={12}>
                    {datePickerInput("endDate", "end date", setter, this.state)}
                </Grid>
            }
            {this.props.options.hasCapacity && <Grid item xs={12}>
                {numberInput("capacity", "capacity", setter, this.state)}
            </Grid>}
            {this.props.options.hasDuration && <Grid item xs={12}>
                {textInput("duration", "duration (in minutes)",
                    setter, this.state, undefined, false, false)}
            </Grid>}
            {this.props.options.hasCategory && <Grid item xs={12}>
                {/* TODO ListDefHierarchicalSelectList */}
                {dropdownList(
                    "category",
                    setter,
                    this.state,
                    "categoryId",
                    this.props.sessionData.getListDefinitionEntriesByListName(GROUPAUXCATEGORY_LISTNAME, undefined, this.state.categoryId).map(ld => listDefToIdName(ld)),
                    {},
                    undefined,
                    true
                )}
            </Grid>}
            {this.props.options.hasReviewDate && <Grid item xs={12}>
                <Grid item xs={12}>
                    {datePickerIso8601Input("endDate", "review date", setter, this.state, false, false)}
                </Grid>
            </Grid>}
        </Grid>;
    }

}
/* @Exemplar - see usage */
export const GroupActivityModal: FC<ModalProps> = props => {
    const eccoAPI = useServicesContext();

    return (
        <ModalCommandForm
            show={true} // i.e. we're a form that's modal when shown
            setShow={props.setShow}
            title={`${props.activity ? "edit" : "create"} ${props.course ? "course" : "session"}`}
            action="save"
            maxWidth="sm"
            afterSave={props.afterSave}
        >
            {form => <GroupActivityEditCommandForm
                {...props}
                readOnly={!eccoAPI.sessionData.hasRoleReferralEdit()}
                sessionData={eccoAPI.sessionData}
                services={eccoAPI}
                commandForm={form}
            />}
        </ModalCommandForm>
    );
};

