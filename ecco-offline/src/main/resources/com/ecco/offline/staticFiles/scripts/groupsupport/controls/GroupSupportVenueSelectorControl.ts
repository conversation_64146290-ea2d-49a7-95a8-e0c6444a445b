import AsyncSelectList = require("../../controls/AsyncSelectList");
import {apiClient} from "ecco-components";
import {listDefToIdName, SessionDataAjaxRepository} from "ecco-dto";
import {IdNameDisabled} from "@eccosolutions/ecco-common";

var repository = new SessionDataAjaxRepository(apiClient);


class GroupSupportVenueSelectorControl extends AsyncSelectList<IdNameDisabled> {

    constructor(onVenueSelected?: (val: number) => void, emptyOptionLabel?: string) {
        super(onVenueSelected, emptyOptionLabel);
    }

    protected fetchViewData(): Promise<IdNameDisabled[]> {
        return repository.getSessionData()
                .then(sd => sd.getListDefinitionEntriesByListName("venue")
                    .map(ld => listDefToIdName(ld)));
    }
}
export = GroupSupportVenueSelectorControl;
