import AsyncSelectList = require("../../controls/AsyncSelectList");
import {apiClient} from "ecco-components";
import {ActivityTypeDto} from "ecco-dto/group-support-dto";
import {GroupSupportAjaxRepository} from "ecco-dto";

var repository = new GroupSupportAjaxRepository(apiClient);


class ActivityTypeSelectorControl extends AsyncSelectList<ActivityTypeDto> {

    private serviceId: number;


    constructor(private onActivityTypeSelected?: (val: number) => void) {
        super(onActivityTypeSelected);
    }

    protected fetchViewData(): Promise<ActivityTypeDto[]> {
        if (this.serviceId) {
            return repository.findAllActivityTypes(this.serviceId);
        }
        return Promise.resolve(null);
    }

    public loadWithService(serviceId: number) {
        this.serviceId = serviceId;
        super.load();
    }
}
export = ActivityTypeSelectorControl;
